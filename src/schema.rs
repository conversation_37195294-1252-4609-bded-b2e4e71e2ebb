// @generated automatically by Diesel CLI.

diesel::table! {
    fields (id) {
        id -> Nullable<Integer>,
        name -> Text,
        parent_field_id -> Nullable<Integer>,
    }
}

diesel::table! {
    growing_area_shapes (id) {
        id -> Integer,
        property_id -> Integer,
        shape_data -> Text,
        shape_type -> Text,
        floor_no -> Integer,
        created_at -> Nullable<Timestamp>,
        updated_at -> Nullable<Timestamp>,
        area -> Nullable<Float>,
    }
}

diesel::table! {
    households (id) {
        id -> Integer,
        name -> Text,
        owner_id -> Integer,
        created_at -> Nullable<Timestamp>,
        updated_at -> Nullable<Timestamp>,
    }
}

diesel::table! {
    notifications (id) {
        id -> Integer,
        user_id -> Integer,
        plant_id -> Integer,
        message -> Text,
        scheduled_time -> Timestamp,
        sent -> Bool,
    }
}

diesel::table! {
    plants (id) {
        id -> Integer,
        name -> Text,
        description -> Nullable<Text>,
        latin_name -> Nullable<Text>,
        variety -> Nullable<Text>,
        note -> Nullable<Text>,
        nutrient_consumption -> Nullable<Text>,
        nutrient_deposit -> Nullable<Text>,
        lighting -> Nullable<Text>,
        temperature -> Nullable<Text>,
        light_amount -> Nullable<Text>,
        sowing_time -> Nullable<Text>,
        propagation_time -> Nullable<Text>,
        harvest_time -> Nullable<Text>,
        growth_duration -> Nullable<Text>,
        harvest_duration -> Nullable<Text>,
        field_id -> Nullable<Integer>,
    }
}

diesel::table! {
    plots (id) {
        id -> Integer,
        name -> Text,
        x -> Integer,
        y -> Integer,
        description -> Nullable<Text>,
        parent_id -> Nullable<Integer>,
    }
}

diesel::table! {
    properties (id) {
        id -> Nullable<Integer>,
        name -> Text,
        inside_area -> Nullable<Float>,
        outside_area -> Nullable<Float>,
        floors -> Integer,
        owner_id -> Integer,
        household_id -> Integer,
        created_at -> Nullable<Timestamp>,
        updated_at -> Nullable<Timestamp>,
    }
}

diesel::table! {
    property_shapes (id) {
        id -> Integer,
        property_id -> Integer,
        floor_no -> Integer,
        shape_data -> Text,
        shape_type -> Text,
        area -> Nullable<Double>,
        created_at -> Nullable<Timestamp>,
        updated_at -> Nullable<Timestamp>,
    }
}

diesel::table! {
    property_shares (user_id, property_id) {
        user_id -> Integer,
        property_id -> Integer,
        permission_level -> Text,
        created_at -> Nullable<Timestamp>,
        updated_at -> Nullable<Timestamp>,
    }
}

diesel::table! {
    season_plan_plants (id) {
        id -> Nullable<Integer>,
        plan_id -> Integer,
        plant_id -> Integer,
        quantity -> Integer,
        position_x -> Nullable<Integer>,
        position_y -> Nullable<Integer>,
        notes -> Nullable<Text>,
        created_at -> Timestamp,
        updated_at -> Timestamp,
    }
}

diesel::table! {
    season_plans (id) {
        id -> Nullable<Integer>,
        name -> Text,
        season_id -> Integer,
        property_id -> Integer,
        growing_area_id -> Nullable<Integer>,
        start_date -> Date,
        end_date -> Date,
        description -> Nullable<Text>,
        created_at -> Timestamp,
        updated_at -> Timestamp,
    }
}

diesel::table! {
    seasons (id) {
        id -> Integer,
        name -> Text,
        start_date -> Date,
        end_date -> Date,
    }
}

diesel::table! {
    seeds (id) {
        id -> Integer,
        name -> Text,
        note -> Nullable<Text>,
        origin -> Nullable<Text>,
        acquisition_year -> Integer,
        expiration_year -> Integer,
        herba_id -> Integer,
    }
}

diesel::table! {
    user_households (user_id, household_id) {
        user_id -> Integer,
        household_id -> Integer,
        role -> Text,
    }
}

diesel::table! {
    user_permissions (user_id, permission) {
        user_id -> Integer,
        permission -> Text,
    }
}

diesel::table! {
    users (id) {
        id -> Integer,
        username -> Text,
        password_hash -> Text,
        role -> Text,
    }
}

diesel::joinable!(households -> users (owner_id));
diesel::joinable!(notifications -> plants (plant_id));
diesel::joinable!(notifications -> users (user_id));
diesel::joinable!(plants -> fields (field_id));
diesel::joinable!(properties -> households (household_id));
diesel::joinable!(properties -> users (owner_id));
diesel::joinable!(property_shares -> properties (property_id));
diesel::joinable!(property_shares -> users (user_id));
diesel::joinable!(season_plan_plants -> plants (plant_id));
diesel::joinable!(season_plan_plants -> season_plans (plan_id));
diesel::joinable!(season_plans -> properties (property_id));
diesel::joinable!(season_plans -> seasons (season_id));
diesel::joinable!(seeds -> plants (herba_id));
diesel::joinable!(user_households -> households (household_id));
diesel::joinable!(user_households -> users (user_id));
diesel::joinable!(user_permissions -> users (user_id));

diesel::allow_tables_to_appear_in_same_query!(
    fields,
    growing_area_shapes,
    households,
    notifications,
    plants,
    plots,
    properties,
    property_shapes,
    property_shares,
    season_plan_plants,
    season_plans,
    seasons,
    seeds,
    user_households,
    user_permissions,
    users,
);
