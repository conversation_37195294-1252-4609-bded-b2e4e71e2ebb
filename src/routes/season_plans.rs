use actix_session::Session;
use actix_web::{web, HttpResponse, Responder};
use chrono::NaiveDate;
use serde::{Deserialize, Serialize};
use serde_json::json;
use diesel::prelude::*;
use r2d2::Pool;
use diesel::r2d2::ConnectionManager;

use crate::models::{GrowingArea, Plant, Property, Season, SeasonPlan, SeasonPlanPlant};
use crate::utils::csrf::{get_csrf_token, verify_csrf_token};
use crate::utils::templates::TEMPLATES;

#[derive(Deserialize)]
pub struct SeasonPlanForm {
    name: String,
    season_id: i32,
    property_id: i32,
    growing_area_id: Option<i32>,
    start_date: String,
    end_date: String,
    description: Option<String>,
    csrf_token: String,
}

#[derive(Deserialize)]
pub struct AddPlantForm {
    plant_id: i32,
    quantity: i32,
    position_x: Option<i32>,
    position_y: Option<i32>,
    notes: Option<String>,
    csrf_token: String,
}

#[derive(Deserialize)]
pub struct UpdatePlantForm {
    plant_id: i32,
    quantity: i32,
    position_x: Option<i32>,
    position_y: Option<i32>,
    notes: Option<String>,
    csrf_token: String,
}

#[derive(Serialize)]
pub struct PlanViewModel {
    id: i32,
    name: String,
    season_id: i32,
    season_name: String,
    property_id: i32,
    property_name: String,
    growing_area_id: Option<i32>,
    growing_area: Option<GrowingArea>,
    start_date: String,
    end_date: String,
    description: Option<String>,
    plants: Vec<PlanPlantViewModel>,
    available_plants: Vec<Plant>,
    plant_count: i32,
}

#[derive(Serialize)]
pub struct PlanPlantViewModel {
    id: i32,
    name: String,
    quantity: i32,
    position_x: Option<i32>,
    position_y: Option<i32>,
    notes: Option<String>,
}

type DbPool = Pool<ConnectionManager<SqliteConnection>>;

// List all season plans
pub async fn index(db: web::Data<DbPool>, session: Session) -> impl Responder {
    let csrf_token = get_csrf_token(&session);

    let mut conn = match db.get() {
        Ok(conn) => conn,
        Err(e) => {
            eprintln!("Error getting database connection: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let plans = match SeasonPlan::find_all(&mut conn) {
        Ok(plans) => plans,
        Err(e) => {
            eprintln!("Error fetching season plans: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let mut plan_view_models = Vec::new();

    for plan in plans {
        let season = match Season::find_by_id(&mut conn, plan.season_id) {
            Ok(Some(season)) => season,
            _ => continue,
        };

        let property = match Property::find_by_id(&mut conn, plan.property_id) {
            Ok(Some(property)) => property,
            _ => continue,
        };

        let growing_area = if let Some(area_id) = plan.growing_area_id {
            match GrowingArea::find_by_id(&mut conn, area_id) {
                Ok(area) => area,
                Err(_) => None,
            }
        } else {
            None
        };

        let plant_count = match SeasonPlanPlant::count_by_plan_id(&mut conn, plan.id.unwrap_or(0)) {
            Ok(count) => count as i32,
            Err(_) => 0,
        };

        plan_view_models.push(PlanViewModel {
            id: plan.id.unwrap_or(0),
            name: plan.name,
            season_id: season.id,
            season_name: season.name,
            property_id: property.id.unwrap_or(0),
            property_name: property.name,
            growing_area_id: plan.growing_area_id,
            growing_area,
            start_date: plan.start_date.format("%Y-%m-%d").to_string(),
            end_date: plan.end_date.format("%Y-%m-%d").to_string(),
            description: plan.description,
            plants: Vec::new(), // Not needed for index view
            available_plants: Vec::new(), // Not needed for index view
            plant_count,
        });
    }

    let html = match tera::Context::from_serialize(json!({
        "plans": plan_view_models,
        "csrf_token": csrf_token,
    })) {
        Ok(context) => {
            match TEMPLATES.render("season_plans/index.html", &context) {
                Ok(html) => html,
                Err(e) => {
                    eprintln!("Template error: {}", e);
                    return HttpResponse::InternalServerError().finish();
                }
            }
        }
        Err(e) => {
            eprintln!("Context serialization error: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    HttpResponse::Ok().content_type("text/html").body(html)
}

// Show form to create a new season plan
pub async fn new(db: web::Data<DbPool>, session: Session) -> impl Responder {
    let csrf_token = get_csrf_token(&session);

    let mut conn = match db.get() {
        Ok(conn) => conn,
        Err(e) => {
            eprintln!("Error getting database connection: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let seasons = match Season::find_all(&mut conn) {
        Ok(seasons) => seasons,
        Err(e) => {
            eprintln!("Error fetching seasons: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let properties = match Property::find_all(&mut conn) {
        Ok(properties) => properties,
        Err(e) => {
            eprintln!("Error fetching properties: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let html = match tera::Context::from_serialize(json!({
        "seasons": seasons,
        "properties": properties,
        "csrf_token": csrf_token,
    })) {
        Ok(context) => {
            match TEMPLATES.render("season_plans/new.html", &context) {
                Ok(html) => html,
                Err(e) => {
                    eprintln!("Template error: {}", e);
                    return HttpResponse::InternalServerError().finish();
                }
            }
        }
        Err(e) => {
            eprintln!("Context serialization error: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    HttpResponse::Ok().content_type("text/html").body(html)
}

// Create a new season plan
pub async fn create(
    db: web::Data<DbPool>,
    form: web::Form<SeasonPlanForm>,
    session: Session,
) -> impl Responder {
    if !verify_csrf_token(&session, &form.csrf_token) {
        return HttpResponse::Forbidden().finish();
    }

    let start_date = match NaiveDate::parse_from_str(&form.start_date, "%Y-%m-%d") {
        Ok(date) => date,
        Err(_) => return HttpResponse::BadRequest().body("Invalid start date format"),
    };

    let end_date = match NaiveDate::parse_from_str(&form.end_date, "%Y-%m-%d") {
        Ok(date) => date,
        Err(_) => return HttpResponse::BadRequest().body("Invalid end date format"),
    };

    if start_date > end_date {
        return HttpResponse::BadRequest().body("Start date must be before end date");
    }

    let mut conn = match db.get() {
        Ok(conn) => conn,
        Err(e) => {
            eprintln!("Error getting database connection: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let new_plan = crate::models::season_plan::NewSeasonPlan {
        name: &form.name,
        season_id: form.season_id,
        property_id: form.property_id,
        growing_area_id: form.growing_area_id,
        start_date,
        end_date,
        description: form.description.as_deref(),
    };

    match SeasonPlan::create(&mut conn, &new_plan) {
        Ok(plan_id) => {
            HttpResponse::SeeOther()
                .append_header(("Location", format!("/season_plans/{}/view", plan_id)))
                .finish()
        }
        Err(e) => {
            eprintln!("Error creating season plan: {}", e);
            HttpResponse::InternalServerError().finish()
        }
    }
}

// View a specific season plan
pub async fn view(
    db: web::Data<DbPool>,
    path: web::Path<i32>,
    session: Session,
) -> impl Responder {
    let plan_id = path.into_inner();
    let csrf_token = get_csrf_token(&session);

    let mut conn = match db.get() {
        Ok(conn) => conn,
        Err(e) => {
            eprintln!("Error getting database connection: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let plan = match SeasonPlan::find_by_id(&mut conn, plan_id) {
        Ok(Some(plan)) => plan,
        Ok(None) => return HttpResponse::NotFound().body("Season plan not found"),
        Err(e) => {
            eprintln!("Error fetching season plan: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let season = match Season::find_by_id(&mut conn, plan.season_id) {
        Ok(Some(season)) => season,
        Ok(None) => return HttpResponse::NotFound().body("Season not found"),
        Err(e) => {
            eprintln!("Error fetching season: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let property = match Property::find_by_id(&mut conn, plan.property_id) {
        Ok(Some(property)) => property,
        Ok(None) => return HttpResponse::NotFound().body("Property not found"),
        Err(e) => {
            eprintln!("Error fetching property: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let growing_area = if let Some(area_id) = plan.growing_area_id {
        match GrowingArea::find_by_id(&mut conn, area_id) {
            Ok(area) => area,
            Err(e) => {
                eprintln!("Error fetching growing area: {}", e);
                None
            }
        }
    } else {
        None
    };

    // Get plants in this plan
    let plan_plants = match SeasonPlanPlant::find_by_plan_id(&mut conn, plan_id) {
        Ok(plants) => plants,
        Err(e) => {
            eprintln!("Error fetching plan plants: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let mut plant_view_models = Vec::new();

    for plan_plant in plan_plants {
        let plant = match Plant::find_by_id(&mut conn, plan_plant.plant_id) {
            Ok(Some(plant)) => plant,
            _ => continue,
        };

        plant_view_models.push(PlanPlantViewModel {
            id: plan_plant.id.unwrap_or(0),
            name: plant.name,
            quantity: plan_plant.quantity,
            position_x: plan_plant.position_x,
            position_y: plan_plant.position_y,
            notes: plan_plant.notes,
        });
    }

    // Get available plants for adding to the plan
    let available_plants = match Plant::find_all(&mut conn) {
        Ok(plants) => plants,
        Err(e) => {
            eprintln!("Error fetching available plants: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let plant_count = plant_view_models.len() as i32;

    let plan_view_model = PlanViewModel {
        id: plan.id.unwrap_or(0),
        name: plan.name,
        season_id: season.id,
        season_name: season.name,
        property_id: property.id.unwrap_or(0),
        property_name: property.name,
        growing_area_id: plan.growing_area_id,
        growing_area,
        start_date: plan.start_date.format("%Y-%m-%d").to_string(),
        end_date: plan.end_date.format("%Y-%m-%d").to_string(),
        description: plan.description,
        plants: plant_view_models,
        available_plants,
        plant_count,
    };

    let html = match tera::Context::from_serialize(json!({
        "plan": plan_view_model,
        "csrf_token": csrf_token,
    })) {
        Ok(context) => {
            match TEMPLATES.render("season_plans/view.html", &context) {
                Ok(html) => html,
                Err(e) => {
                    eprintln!("Template error: {}", e);
                    return HttpResponse::InternalServerError().finish();
                }
            }
        }
        Err(e) => {
            eprintln!("Context serialization error: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    HttpResponse::Ok().content_type("text/html").body(html)
}

// Show form to edit a season plan
pub async fn edit(
    db: web::Data<DbPool>,
    path: web::Path<i32>,
    session: Session,
) -> impl Responder {
    let plan_id = path.into_inner();
    let csrf_token = get_csrf_token(&session);

    let mut conn = match db.get() {
        Ok(conn) => conn,
        Err(e) => {
            eprintln!("Error getting database connection: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let plan = match SeasonPlan::find_by_id(&mut conn, plan_id) {
        Ok(Some(plan)) => plan,
        Ok(None) => return HttpResponse::NotFound().body("Season plan not found"),
        Err(e) => {
            eprintln!("Error fetching season plan: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let seasons = match Season::find_all(&mut conn) {
        Ok(seasons) => seasons,
        Err(e) => {
            eprintln!("Error fetching seasons: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let properties = match Property::find_all(&mut conn) {
        Ok(properties) => properties,
        Err(e) => {
            eprintln!("Error fetching properties: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let growing_areas = match GrowingArea::find_by_property_id(&mut conn, plan.property_id) {
        Ok(areas) => areas,
        Err(e) => {
            eprintln!("Error fetching growing areas: {}", e);
            Vec::new()
        }
    };

    let html = match tera::Context::from_serialize(json!({
        "plan": plan,
        "seasons": seasons,
        "properties": properties,
        "growing_areas": growing_areas,
        "start_date": plan.start_date.format("%Y-%m-%d").to_string(),
        "end_date": plan.end_date.format("%Y-%m-%d").to_string(),
        "csrf_token": csrf_token,
    })) {
        Ok(context) => {
            match TEMPLATES.render("season_plans/edit.html", &context) {
                Ok(html) => html,
                Err(e) => {
                    eprintln!("Template error: {}", e);
                    return HttpResponse::InternalServerError().finish();
                }
            }
        }
        Err(e) => {
            eprintln!("Context serialization error: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    HttpResponse::Ok().content_type("text/html").body(html)
}

// Update a season plan
pub async fn update(
    db: web::Data<DbPool>,
    path: web::Path<i32>,
    form: web::Form<SeasonPlanForm>,
    session: Session,
) -> impl Responder {
    let plan_id = path.into_inner();

    if !verify_csrf_token(&session, &form.csrf_token) {
        return HttpResponse::Forbidden().finish();
    }

    let mut conn = match db.get() {
        Ok(conn) => conn,
        Err(e) => {
            eprintln!("Error getting database connection: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    // Verify the plan exists
    match SeasonPlan::find_by_id(&mut conn, plan_id) {
        Ok(Some(_)) => (),
        Ok(None) => return HttpResponse::NotFound().body("Season plan not found"),
        Err(e) => {
            eprintln!("Error fetching season plan: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let start_date = match NaiveDate::parse_from_str(&form.start_date, "%Y-%m-%d") {
        Ok(date) => date,
        Err(_) => return HttpResponse::BadRequest().body("Invalid start date format"),
    };

    let end_date = match NaiveDate::parse_from_str(&form.end_date, "%Y-%m-%d") {
        Ok(date) => date,
        Err(_) => return HttpResponse::BadRequest().body("Invalid end date format"),
    };

    if start_date > end_date {
        return HttpResponse::BadRequest().body("Start date must be before end date");
    }

    let updated_plan = crate::models::season_plan::NewSeasonPlan {
        name: &form.name,
        season_id: form.season_id,
        property_id: form.property_id,
        growing_area_id: form.growing_area_id,
        start_date,
        end_date,
        description: form.description.as_deref(),
    };

    match SeasonPlan::update(&mut conn, plan_id, &updated_plan) {
        Ok(_) => {
            HttpResponse::SeeOther()
                .append_header(("Location", format!("/season_plans/{}/view", plan_id)))
                .finish()
        }
        Err(e) => {
            eprintln!("Error updating season plan: {}", e);
            HttpResponse::InternalServerError().finish()
        }
    }
}

// Delete a season plan
pub async fn delete(
    db: web::Data<DbPool>,
    path: web::Path<i32>,
    form: web::Form<serde_json::Value>,
    session: Session,
) -> impl Responder {
    let plan_id = path.into_inner();

    // Extract CSRF token from form
    let csrf_token = match form.get("csrf_token").and_then(|v| v.as_str()) {
        Some(token) => token,
        None => return HttpResponse::BadRequest().body("Missing CSRF token"),
    };

    if !verify_csrf_token(&session, csrf_token) {
        return HttpResponse::Forbidden().finish();
    }

    let mut conn = match db.get() {
        Ok(conn) => conn,
        Err(e) => {
            eprintln!("Error getting database connection: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    // First delete all plants in this plan
    match SeasonPlanPlant::delete_by_plan_id(&mut conn, plan_id) {
        Ok(_) => (),
        Err(e) => {
            eprintln!("Error deleting plan plants: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    }

    // Then delete the plan itself
    match SeasonPlan::delete(&mut conn, plan_id) {
        Ok(_) => {
            HttpResponse::SeeOther()
                .append_header(("Location", "/season_plans"))
                .finish()
        }
        Err(e) => {
            eprintln!("Error deleting season plan: {}", e);
            HttpResponse::InternalServerError().finish()
        }
    }
}

// Add a plant to a season plan
pub async fn add_plant(
    db: web::Data<DbPool>,
    path: web::Path<i32>,
    form: web::Form<AddPlantForm>,
    session: Session,
) -> impl Responder {
    let plan_id = path.into_inner();

    if !verify_csrf_token(&session, &form.csrf_token) {
        return HttpResponse::Forbidden().finish();
    }

    let mut conn = match db.get() {
        Ok(conn) => conn,
        Err(e) => {
            eprintln!("Error getting database connection: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    // Verify the plan exists
    match SeasonPlan::find_by_id(&mut conn, plan_id) {
        Ok(Some(_)) => (),
        Ok(None) => return HttpResponse::NotFound().body("Season plan not found"),
        Err(e) => {
            eprintln!("Error fetching season plan: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    }

    // Verify the plant exists
    match Plant::find_by_id(&mut conn, form.plant_id) {
        Ok(Some(_)) => (),
        Ok(None) => return HttpResponse::NotFound().body("Plant not found"),
        Err(e) => {
            eprintln!("Error fetching plant: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    }

    let new_plant = crate::models::season_plan_plant::NewSeasonPlanPlant {
        plan_id,
        plant_id: form.plant_id,
        quantity: form.quantity,
        position_x: form.position_x,
        position_y: form.position_y,
        notes: form.notes.as_deref(),
    };

    match SeasonPlanPlant::create(&mut conn, &new_plant) {
        Ok(_) => {
            HttpResponse::SeeOther()
                .append_header(("Location", format!("/season_plans/{}/view", plan_id)))
                .finish()
        }
        Err(e) => {
            eprintln!("Error adding plant to plan: {}", e);
            HttpResponse::InternalServerError().finish()
        }
    }
}

// Show form to edit a plant in a season plan
pub async fn edit_plant(
    db: web::Data<DbPool>,
    path: web::Path<(i32, i32)>,
    session: Session,
) -> impl Responder {
    let (plan_id, plant_id) = path.into_inner();
    let csrf_token = get_csrf_token(&session);

    let mut conn = match db.get() {
        Ok(conn) => conn,
        Err(e) => {
            eprintln!("Error getting database connection: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let plan = match SeasonPlan::find_by_id(&mut conn, plan_id) {
        Ok(Some(plan)) => plan,
        Ok(None) => return HttpResponse::NotFound().body("Season plan not found"),
        Err(e) => {
            eprintln!("Error fetching season plan: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let plan_plant = match SeasonPlanPlant::find_by_id(&mut conn, plant_id) {
        Ok(Some(plant)) => plant,
        Ok(None) => return HttpResponse::NotFound().body("Plant in plan not found"),
        Err(e) => {
            eprintln!("Error fetching plant in plan: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let plant = match Plant::find_by_id(&mut conn, plan_plant.plant_id) {
        Ok(Some(plant)) => plant,
        Ok(None) => return HttpResponse::NotFound().body("Plant not found"),
        Err(e) => {
            eprintln!("Error fetching plant: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let plant_view_model = PlanPlantViewModel {
        id: plan_plant.id.unwrap_or(0),
        name: plant.name,
        quantity: plan_plant.quantity,
        position_x: plan_plant.position_x,
        position_y: plan_plant.position_y,
        notes: plan_plant.notes,
    };

    let html = match tera::Context::from_serialize(json!({
        "plan": plan,
        "plant": plant_view_model,
        "csrf_token": csrf_token,
    })) {
        Ok(context) => {
            match TEMPLATES.render("season_plans/edit_plant.html", &context) {
                Ok(html) => html,
                Err(e) => {
                    eprintln!("Template error: {}", e);
                    return HttpResponse::InternalServerError().finish();
                }
            }
        }
        Err(e) => {
            eprintln!("Context serialization error: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    HttpResponse::Ok().content_type("text/html").body(html)
}

// Update a plant in a season plan
pub async fn update_plant(
    db: web::Data<DbPool>,
    path: web::Path<(i32, i32)>,
    form: web::Form<UpdatePlantForm>,
    session: Session,
) -> impl Responder {
    let (plan_id, plant_id) = path.into_inner();

    if !verify_csrf_token(&session, &form.csrf_token) {
        return HttpResponse::Forbidden().finish();
    }

    let mut conn = match db.get() {
        Ok(conn) => conn,
        Err(e) => {
            eprintln!("Error getting database connection: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    // Verify the plant exists
    match SeasonPlanPlant::find_by_id(&mut conn, plant_id) {
        Ok(Some(_)) => (),
        Ok(None) => return HttpResponse::NotFound().body("Plant in plan not found"),
        Err(e) => {
            eprintln!("Error fetching plant in plan: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let updated_plant = crate::models::season_plan_plant::NewSeasonPlanPlant {
        plan_id,
        plant_id: form.plant_id,
        quantity: form.quantity,
        position_x: form.position_x,
        position_y: form.position_y,
        notes: form.notes.as_deref(),
    };

    match SeasonPlanPlant::update(&mut conn, plant_id, &updated_plant) {
        Ok(_) => {
            HttpResponse::SeeOther()
                .append_header(("Location", format!("/season_plans/{}/view", plan_id)))
                .finish()
        }
        Err(e) => {
            eprintln!("Error updating plant in plan: {}", e);
            HttpResponse::InternalServerError().finish()
        }
    }
}

// Remove a plant from a season plan
pub async fn remove_plant(
    db: web::Data<DbPool>,
    path: web::Path<(i32, i32)>,
    form: web::Form<serde_json::Value>,
    session: Session,
) -> impl Responder {
    let (plan_id, plant_id) = path.into_inner();

    // Extract CSRF token from form
    let csrf_token = match form.get("csrf_token").and_then(|v| v.as_str()) {
        Some(token) => token,
        None => return HttpResponse::BadRequest().body("Missing CSRF token"),
    };

    if !verify_csrf_token(&session, csrf_token) {
        return HttpResponse::Forbidden().finish();
    }

    let mut conn = match db.get() {
        Ok(conn) => conn,
        Err(e) => {
            eprintln!("Error getting database connection: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    match SeasonPlanPlant::delete(&mut conn, plant_id) {
        Ok(_) => {
            HttpResponse::SeeOther()
                .append_header(("Location", format!("/season_plans/{}/view", plan_id)))
                .finish()
        }
        Err(e) => {
            eprintln!("Error removing plant from plan: {}", e);
            HttpResponse::InternalServerError().finish()
        }
    }
}

// API endpoint to get growing areas for a property
pub async fn get_property_growing_areas(
    db: web::Data<DbPool>,
    path: web::Path<i32>,
) -> impl Responder {
    let property_id = path.into_inner();

    let mut conn = match db.get() {
        Ok(conn) => conn,
        Err(e) => {
            eprintln!("Error getting database connection: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    let areas = match GrowingArea::find_by_property_id(&mut conn, property_id) {
        Ok(areas) => areas,
        Err(e) => {
            eprintln!("Error fetching growing areas: {}", e);
            return HttpResponse::InternalServerError().finish();
        }
    };

    HttpResponse::Ok().json(areas)
}

pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/season_plans")
            .route("", web::get().to(index))
            .route("/new", web::get().to(new))
            .route("/create", web::post().to(create))
            .route("/{id}/view", web::get().to(view))
            .route("/{id}/edit", web::get().to(edit))
            .route("/{id}/update", web::post().to(update))
            .route("/{id}/delete", web::post().to(delete))
            .route("/{id}/add_plant", web::post().to(add_plant))
            .route("/{id}/plants/{plant_id}/edit", web::get().to(edit_plant))
            .route("/{id}/plants/{plant_id}/update", web::post().to(update_plant))
            .route("/{id}/plants/{plant_id}/remove", web::post().to(remove_plant))
            .route("/api/properties/{id}/growing_areas", web::get().to(get_property_growing_areas))
    );
}
