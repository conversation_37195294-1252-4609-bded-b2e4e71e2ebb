use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use crate::schema::plants as plants_table;

#[derive(Debug, Clone, Queryable, Identifiable, Serialize, Deserialize, AsChangeset, Selectable)]
#[diesel(table_name = plants_table)]
pub struct Plant {
    pub id: i32,
    pub name: String,
    pub description: Option<String>,
    pub latin_name: Option<String>,
    pub variety: Option<String>,
    pub note: Option<String>,
    pub nutrient_consumption: Option<String>,
    pub nutrient_deposit: Option<String>,
    pub lighting: Option<String>,
    pub temperature: Option<String>,
    pub light_amount: Option<String>,
    pub sowing_time: Option<String>,
    pub propagation_time: Option<String>,
    pub harvest_time: Option<String>,
    pub growth_duration: Option<String>,
    pub harvest_duration: Option<String>,
    pub field_id: Option<i32>,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = plants_table)]
pub struct NewPlant<'a> {
    pub name: &'a str,
    pub description: Option<&'a str>,
    pub latin_name: Option<&'a str>,
    pub variety: Option<&'a str>,
    pub note: Option<&'a str>,
    pub nutrient_consumption: Option<&'a str>,
    pub nutrient_deposit: Option<&'a str>,
    pub lighting: Option<&'a str>,
    pub temperature: Option<&'a str>,
    pub light_amount: Option<&'a str>,
    pub sowing_time: Option<&'a str>,
    pub propagation_time: Option<&'a str>,
    pub harvest_time: Option<&'a str>,
    pub growth_duration: Option<&'a str>,
    pub harvest_duration: Option<&'a str>,
    pub field_id: Option<i32>,
}

impl Plant {
    pub fn find_all(conn: &mut SqliteConnection) -> QueryResult<Vec<Plant>> {
        plants_table::table.load::<Plant>(conn)
    }

    pub fn find_by_id(conn: &mut SqliteConnection, plant_id: i32) -> QueryResult<Option<Plant>> {
        plants_table::table
            .filter(plants_table::id.eq(plant_id))
            .first(conn)
            .optional()
    }
}
