{% extends "base.html" %}

{% block title %}Edit Season Plan{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-green-800">Edit Season Plan</h1>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
        <form method="post" action="/season_plans/{{ plan.id }}/update">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Plan Name</label>
                    <input type="text" id="name" name="name" value="{{ plan.name }}" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                </div>

                <div class="mb-4">
                    <label for="season_id" class="block text-sm font-medium text-gray-700 mb-1">Season</label>
                    <select id="season_id" name="season_id" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                        <option value="">Select a season</option>
                        {% for season in seasons %}
                        <option value="{{ season.id }}" {% if season.id == plan.season_id %}selected{% endif %}>{{ season.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="mb-4">
                    <label for="property_id" class="block text-sm font-medium text-gray-700 mb-1">Property</label>
                    <select id="property_id" name="property_id" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                        <option value="">Select a property</option>
                        {% for property in properties %}
                        <option value="{{ property.id }}" {% if property.id == plan.property_id %}selected{% endif %}>{{ property.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="mb-4">
                    <label for="growing_area_id" class="block text-sm font-medium text-gray-700 mb-1">Growing Area (Optional)</label>
                    <select id="growing_area_id" name="growing_area_id"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                        <option value="">Select a growing area</option>
                        {% for area in growing_areas %}
                        <option value="{{ area.id }}" {% if plan.growing_area_id and area.id == plan.growing_area_id %}selected{% endif %}>
                            {% if area.name %}{{ area.name }}{% else %}Area {{ area.id }}{% endif %}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <div class="mb-4">
                    <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    <input type="date" id="start_date" name="start_date" value="{{ start_date }}" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                </div>

                <div class="mb-4">
                    <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    <input type="date" id="end_date" name="end_date" value="{{ end_date }}" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                </div>

                <div class="col-span-2">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description (Optional)</label>
                    <textarea id="description" name="description" rows="3"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">{% if plan.description %}{{ plan.description }}{% endif %}</textarea>
                </div>
            </div>

            <div class="mt-6 flex justify-end">
                <a href="/season_plans/{{ plan.id }}/view" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded mr-2">
                    Cancel
                </a>
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Update Plan
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const propertySelect = document.getElementById('property_id');
        const growingAreaSelect = document.getElementById('growing_area_id');
        const currentGrowingAreaId = '{{ plan.growing_area_id|default("") }}';

        propertySelect.addEventListener('change', async function() {
            const propertyId = this.value;
            if (!propertyId) {
                growingAreaSelect.innerHTML = '<option value="">Select a growing area</option>';
                return;
            }

            try {
                const response = await fetch(`/api/properties/${propertyId}/growing_areas`);
                const areas = await response.json();

                let options = '<option value="">Select a growing area</option>';
                areas.forEach(area => {
                    const selected = area.id.toString() === currentGrowingAreaId ? 'selected' : '';
                    options += `<option value="${area.id}" ${selected}>${area.name || 'Area ' + area.id}</option>`;
                });

                growingAreaSelect.innerHTML = options;
            } catch (error) {
                console.error('Error fetching growing areas:', error);
            }
        });
    });
</script>
{% endblock %}