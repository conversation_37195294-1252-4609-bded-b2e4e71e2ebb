{% extends "base.html" %}

{% block title %}Season Plans{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-green-800">Season Plans</h1>
        <a href="/season_plans/new" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
            Create New Plan
        </a>
    </div>

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        {% if plans %}
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Plan Name
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Season
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Property
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date Range
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Plants
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for plan in plans %}
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">{{ plan.name }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ plan.season_name }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ plan.property_name }}</div>
                        {% if plan.growing_area %}
                        <div class="text-xs text-gray-500">{{ plan.growing_area.name|default('Area ' ~ plan.growing_area.id) }}</div>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ plan.start_date }} to {{ plan.end_date }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ plan.plant_count|default(0) }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <a href="/season_plans/{{ plan.id }}/view" class="text-green-600 hover:text-green-900 mr-3">View</a>
                        <a href="/season_plans/{{ plan.id }}/edit" class="text-blue-600 hover:text-blue-900 mr-3">Edit</a>
                        <form method="post" action="/season_plans/{{ plan.id }}/delete" class="inline">
                            <button type="submit" class="text-red-600 hover:text-red-900"
                                onclick="return confirm('Are you sure you want to delete this plan?')">
                                Delete
                            </button>
                        </form>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div class="text-center py-8">
            <p class="text-gray-500 mb-4">No season plans found.</p>
            <a href="/season_plans/new" class="text-green-600 hover:text-green-700 font-medium">
                Create your first season plan
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
