use actix_web::{test, web, App};
use actix_session::{storage::CookieSessionStore, SessionMiddleware};
use actix_web::cookie::Key;
use diesel::r2d2::{self, ConnectionManager};
use diesel::{SqliteConnection, RunQueryDsl};
use garden_planner_web::{routes, DbPool};
use serde_json::json;
use std::sync::Once;

static INIT: Once = Once::new();

fn setup_test_db() -> DbPool {
    INIT.call_once(|| {
        std::env::set_var("DATABASE_URL", "sqlite://test_gardening_app.db");
        std::env::set_var("RUST_LOG", "debug");
        env_logger::init();
    });

    let database_url = std::env::var("DATABASE_URL").expect("DATABASE_URL must be set");
    let manager = ConnectionManager::<SqliteConnection>::new(database_url);
    let pool = r2d2::Pool::builder()
        .build(manager)
        .expect("Failed to create pool.");

    // Run migrations
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");
    
    // Clean up existing data for fresh tests
    diesel::sql_query("DELETE FROM season_plan_plants").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM season_plans").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM growing_area_shapes").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM property_shapes").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM property_shares").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM properties").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM user_households").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM households").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM seeds").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM plants").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM users WHERE username != 'admin'").execute(&mut conn).ok();
    
    pool
}

fn create_test_app(pool: DbPool) -> actix_web::dev::Service<
    actix_web::dev::ServiceRequest,
    actix_web::dev::ServiceResponse<actix_web::body::EitherBody<actix_web::body::BoxBody>>,
    actix_web::Error,
> {
    let secret_key = Key::generate();
    
    test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                secret_key.clone(),
            ))
            .app_data(web::Data::new(pool))
            .configure(routes::init)
    )
}

#[actix_web::test]
async fn test_homepage_loads() {
    let pool = setup_test_db();
    let app = create_test_app(pool).await;

    let req = test::TestRequest::get().uri("/").to_request();
    let resp = test::call_service(&app, req).await;
    
    assert!(resp.status().is_success());
}

#[actix_web::test]
async fn test_user_registration_and_login() {
    let pool = setup_test_db();
    let app = create_test_app(pool).await;

    // Test registration
    let registration_data = json!({
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpassword123",
        "confirm_password": "testpassword123"
    });

    let req = test::TestRequest::post()
        .uri("/auth/register")
        .set_form(&registration_data)
        .to_request();
    
    let resp = test::call_service(&app, req).await;
    assert!(resp.status().is_redirection() || resp.status().is_success());

    // Test login
    let login_data = json!({
        "username": "testuser",
        "password": "testpassword123"
    });

    let req = test::TestRequest::post()
        .uri("/auth/login")
        .set_form(&login_data)
        .to_request();
    
    let resp = test::call_service(&app, req).await;
    assert!(resp.status().is_redirection() || resp.status().is_success());
}

#[actix_web::test]
async fn test_plant_database_operations() {
    let pool = setup_test_db();
    let app = create_test_app(pool).await;

    // First login as admin
    let login_data = json!({
        "username": "admin",
        "password": "admin123"
    });

    let req = test::TestRequest::post()
        .uri("/auth/login")
        .set_form(&login_data)
        .to_request();
    
    let resp = test::call_service(&app, req).await;
    let cookies = resp.response().cookies().collect::<Vec<_>>();

    // Create a plant
    let plant_data = json!({
        "name": "Test Tomato",
        "latin_name": "Solanum lycopersicum",
        "description": "A test tomato plant",
        "variety": "Cherry",
        "sowing_time": "March-April",
        "harvest_time": "July-September"
    });

    let mut req = test::TestRequest::post()
        .uri("/plants/create")
        .set_form(&plant_data);
    
    for cookie in cookies.iter() {
        req = req.cookie(cookie.clone());
    }
    
    let resp = test::call_service(&app, req.to_request()).await;
    assert!(resp.status().is_redirection() || resp.status().is_success());

    // List plants
    let mut req = test::TestRequest::get().uri("/plants/list");
    for cookie in cookies.iter() {
        req = req.cookie(cookie.clone());
    }
    
    let resp = test::call_service(&app, req.to_request()).await;
    assert!(resp.status().is_success());
}

#[actix_web::test]
async fn test_seed_database_operations() {
    let pool = setup_test_db();
    let app = create_test_app(pool).await;

    // Login as admin
    let login_data = json!({
        "username": "admin",
        "password": "admin123"
    });

    let req = test::TestRequest::post()
        .uri("/auth/login")
        .set_form(&login_data)
        .to_request();
    
    let resp = test::call_service(&app, req).await;
    let cookies = resp.response().cookies().collect::<Vec<_>>();

    // List seeds
    let mut req = test::TestRequest::get().uri("/seeds/list");
    for cookie in cookies.iter() {
        req = req.cookie(cookie.clone());
    }
    
    let resp = test::call_service(&app, req.to_request()).await;
    assert!(resp.status().is_success());
}

#[actix_web::test]
async fn test_season_plans_operations() {
    let pool = setup_test_db();
    let app = create_test_app(pool).await;

    // Login as admin
    let login_data = json!({
        "username": "admin",
        "password": "admin123"
    });

    let req = test::TestRequest::post()
        .uri("/auth/login")
        .set_form(&login_data)
        .to_request();
    
    let resp = test::call_service(&app, req).await;
    let cookies = resp.response().cookies().collect::<Vec<_>>();

    // List season plans
    let mut req = test::TestRequest::get().uri("/season_plans");
    for cookie in cookies.iter() {
        req = req.cookie(cookie.clone());
    }
    
    let resp = test::call_service(&app, req.to_request()).await;
    assert!(resp.status().is_success());

    // Access new season plan form
    let mut req = test::TestRequest::get().uri("/season_plans/new");
    for cookie in cookies.iter() {
        req = req.cookie(cookie.clone());
    }
    
    let resp = test::call_service(&app, req.to_request()).await;
    assert!(resp.status().is_success());
}
