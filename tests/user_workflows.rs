use actix_web::{test, web, App};
use actix_session::{storage::CookieSessionStore, SessionMiddleware};
use actix_web::cookie::Key;
use diesel::r2d2::{self, ConnectionManager};
use diesel::{SqliteConnection, RunQueryDsl};
use garden_planner_web::{routes, DbPool};
use serde_json::json;
use std::sync::Once;

static INIT: Once = Once::new();

fn setup_test_db() -> DbPool {
    INIT.call_once(|| {
        std::env::set_var("DATABASE_URL", "sqlite://test_workflow_gardening_app.db");
        std::env::set_var("RUST_LOG", "debug");
        env_logger::init();
    });

    let database_url = std::env::var("DATABASE_URL").expect("DATABASE_URL must be set");
    let manager = ConnectionManager::<SqliteConnection>::new(database_url);
    let pool = r2d2::Pool::builder()
        .build(manager)
        .expect("Failed to create pool.");

    // Clean up existing data for fresh tests
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");
    diesel::sql_query("DELETE FROM season_plan_plants").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM season_plans").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM growing_area_shapes").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM property_shapes").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM property_shares").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM properties").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM user_households").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM households").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM seeds").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM plants").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM users WHERE username != 'admin'").execute(&mut conn).ok();
    
    pool
}

fn create_test_app(pool: DbPool) -> actix_web::dev::Service<
    actix_web::dev::ServiceRequest,
    actix_web::dev::ServiceResponse<actix_web::body::EitherBody<actix_web::body::BoxBody>>,
    actix_web::Error,
> {
    let secret_key = Key::generate();
    
    test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                secret_key.clone(),
            ))
            .app_data(web::Data::new(pool))
            .configure(routes::init)
    )
}

async fn login_user(app: &actix_web::dev::Service<
    actix_web::dev::ServiceRequest,
    actix_web::dev::ServiceResponse<actix_web::body::EitherBody<actix_web::body::BoxBody>>,
    actix_web::Error,
>, username: &str, password: &str) -> Vec<actix_web::cookie::Cookie<'static>> {
    let login_data = json!({
        "username": username,
        "password": password
    });

    let req = test::TestRequest::post()
        .uri("/auth/login")
        .set_form(&login_data)
        .to_request();
    
    let resp = test::call_service(app, req).await;
    resp.response().cookies().collect::<Vec<_>>()
}

#[actix_web::test]
async fn test_complete_user_workflow() {
    let pool = setup_test_db();
    let app = create_test_app(pool).await;

    // Step 1: Register a new user
    let registration_data = json!({
        "username": "gardener1",
        "email": "<EMAIL>",
        "password": "password123",
        "confirm_password": "password123"
    });

    let req = test::TestRequest::post()
        .uri("/auth/register")
        .set_form(&registration_data)
        .to_request();
    
    let resp = test::call_service(&app, req).await;
    assert!(resp.status().is_redirection() || resp.status().is_success());

    // Step 2: Login as the new user
    let cookies = login_user(&app, "gardener1", "password123").await;
    assert!(!cookies.is_empty());

    // Step 3: Create household through wizard
    let household_data = json!({
        "name": "Test Garden Household",
        "description": "A test household for gardening"
    });

    let mut req = test::TestRequest::post()
        .uri("/wizard/household")
        .set_form(&household_data);
    
    for cookie in cookies.iter() {
        req = req.cookie(cookie.clone());
    }
    
    let resp = test::call_service(&app, req.to_request()).await;
    assert!(resp.status().is_redirection() || resp.status().is_success());

    // Step 4: Create property
    let property_data = json!({
        "name": "Test Garden Property",
        "address": "123 Garden Street",
        "description": "A beautiful test garden"
    });

    let mut req = test::TestRequest::post()
        .uri("/wizard/property")
        .set_form(&property_data);
    
    for cookie in cookies.iter() {
        req = req.cookie(cookie.clone());
    }
    
    let resp = test::call_service(&app, req.to_request()).await;
    assert!(resp.status().is_redirection() || resp.status().is_success());

    // Step 5: Access property drawing interface
    let mut req = test::TestRequest::get().uri("/wizard/draw_property_shapes");
    for cookie in cookies.iter() {
        req = req.cookie(cookie.clone());
    }
    
    let resp = test::call_service(&app, req.to_request()).await;
    assert!(resp.status().is_success());

    // Step 6: Access growing areas drawing interface
    let mut req = test::TestRequest::get().uri("/wizard/draw_growing_areas");
    for cookie in cookies.iter() {
        req = req.cookie(cookie.clone());
    }
    
    let resp = test::call_service(&app, req.to_request()).await;
    assert!(resp.status().is_success());

    // Step 7: Add plants to database
    let plant_data = json!({
        "name": "Test Basil",
        "latin_name": "Ocimum basilicum",
        "description": "Aromatic herb",
        "variety": "Sweet Basil",
        "sowing_time": "April-May",
        "harvest_time": "June-October"
    });

    let mut req = test::TestRequest::post()
        .uri("/plants/create")
        .set_form(&plant_data);
    
    for cookie in cookies.iter() {
        req = req.cookie(cookie.clone());
    }
    
    let resp = test::call_service(&app, req.to_request()).await;
    assert!(resp.status().is_redirection() || resp.status().is_success());

    // Step 8: Add seeds to database
    let mut req = test::TestRequest::get().uri("/seeds/new");
    for cookie in cookies.iter() {
        req = req.cookie(cookie.clone());
    }
    
    let resp = test::call_service(&app, req.to_request()).await;
    assert!(resp.status().is_success());

    // Step 9: Create season plan
    let mut req = test::TestRequest::get().uri("/season_plans/new");
    for cookie in cookies.iter() {
        req = req.cookie(cookie.clone());
    }
    
    let resp = test::call_service(&app, req.to_request()).await;
    assert!(resp.status().is_success());

    // Step 10: View property (should work after property creation)
    let mut req = test::TestRequest::get().uri("/properties/list");
    for cookie in cookies.iter() {
        req = req.cookie(cookie.clone());
    }
    
    let resp = test::call_service(&app, req.to_request()).await;
    assert!(resp.status().is_success());
}

#[actix_web::test]
async fn test_admin_user_management_workflow() {
    let pool = setup_test_db();
    let app = create_test_app(pool).await;

    // Step 1: Login as admin
    let admin_cookies = login_user(&app, "admin", "admin123").await;
    assert!(!admin_cookies.is_empty());

    // Step 2: Create a new user manually via admin interface
    let mut req = test::TestRequest::get().uri("/admin/users");
    for cookie in admin_cookies.iter() {
        req = req.cookie(cookie.clone());
    }
    
    let resp = test::call_service(&app, req.to_request()).await;
    assert!(resp.status().is_success());

    // Step 3: Register a second user normally
    let registration_data = json!({
        "username": "gardener2",
        "email": "<EMAIL>",
        "password": "password123",
        "confirm_password": "password123"
    });

    let req = test::TestRequest::post()
        .uri("/auth/register")
        .set_form(&registration_data)
        .to_request();
    
    let resp = test::call_service(&app, req).await;
    assert!(resp.status().is_redirection() || resp.status().is_success());

    // Step 4: Login as the second user
    let user2_cookies = login_user(&app, "gardener2", "password123").await;
    assert!(!user2_cookies.is_empty());

    // Step 5: Create household as user2
    let household_data = json!({
        "name": "Shared Garden Household",
        "description": "A household to be shared"
    });

    let mut req = test::TestRequest::post()
        .uri("/wizard/household")
        .set_form(&household_data);
    
    for cookie in user2_cookies.iter() {
        req = req.cookie(cookie.clone());
    }
    
    let resp = test::call_service(&app, req.to_request()).await;
    assert!(resp.status().is_redirection() || resp.status().is_success());

    // Step 6: Create seed wishlist as user2
    let mut req = test::TestRequest::get().uri("/seeds/list");
    for cookie in user2_cookies.iter() {
        req = req.cookie(cookie.clone());
    }
    
    let resp = test::call_service(&app, req.to_request()).await;
    assert!(resp.status().is_success());

    // Step 7: Login back as admin and check access to shared resources
    let admin_cookies_new = login_user(&app, "admin", "admin123").await;
    
    let mut req = test::TestRequest::get().uri("/seeds/list");
    for cookie in admin_cookies_new.iter() {
        req = req.cookie(cookie.clone());
    }
    
    let resp = test::call_service(&app, req.to_request()).await;
    assert!(resp.status().is_success());
}

#[actix_web::test]
async fn test_watering_notification_workflow() {
    let pool = setup_test_db();
    let app = create_test_app(pool).await;

    // Login as admin
    let cookies = login_user(&app, "admin", "admin123").await;

    // Access notifications
    let mut req = test::TestRequest::get().uri("/notifications/list");
    for cookie in cookies.iter() {
        req = req.cookie(cookie.clone());
    }
    
    let resp = test::call_service(&app, req.to_request()).await;
    assert!(resp.status().is_success());

    // Create a new notification
    let mut req = test::TestRequest::get().uri("/notifications/new");
    for cookie in cookies.iter() {
        req = req.cookie(cookie.clone());
    }
    
    let resp = test::call_service(&app, req.to_request()).await;
    assert!(resp.status().is_success());
}
